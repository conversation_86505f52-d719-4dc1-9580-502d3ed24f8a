import Link from "next/link";
import { getAllPosts } from "@/lib/blog";

export async function BlogList({ limit }: { limit?: number }) {
  const posts = await getAllPosts();
  const visible = limit ? posts.slice(0, limit) : posts;

  if (visible.length === 0) return <p>Nessun articolo disponibile.</p>;

  return (
    <ul className="space-y-6">
      {visible.map((post) => (
        <li key={post.id}>
          <Link href={`/blog/${post.slug}`} className="text-2xl font-semibold hover:underline">
            {post.title}
          </Link>
          <p className="text-sm text-muted-foreground">{post.excerpt}</p>
        </li>
      ))}
    </ul>
  );
}
