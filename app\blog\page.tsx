import Link from "next/link";
import type { Metadata } from "next";
import { getAllPosts } from "@/lib/blog";

export const metadata: Metadata = {
  title: "Blog | Scabbia Rimedi",
  description: "Articoli in italiano su come combattere la scabbia",
};

export default async function BlogPage() {
  const posts = await getAllPosts();

  return (
    <main className="max-w-4xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold mb-6">Blog</h1>
      <ul className="space-y-6">
        {posts.map((post) => (
          <li key={post.id}>
            <Link
              href={`/blog/${post.slug}`}
              className="text-2xl font-semibold hover:underline"
            >
              {post.title}
            </Link>
            <p className="text-sm text-muted-foreground">{post.excerpt}</p>
          </li>
        ))}
      </ul>
    </main>
  );
}
