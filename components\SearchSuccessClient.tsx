'use client';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';

type Report = {
  probability: number;
  summary: string;
  details: string;
  analysis?: string;
};

type InputData = {
  itching_intensity: string;
  itching_worse_at_night: boolean;
  rash_present: boolean;
  rash_locations: string[];
  visible_burrows: boolean;
  onset_days: number;
  close_contact_scabies: boolean;
  lives_in_crowded_setting: boolean;
  attempted_treatment: string;
  skin_scraping_positive: boolean;
  immune_status: string;
  thick_crusts_on_skin: boolean;
};

type SessionData = {
  input: InputData;
  probability: number;
  summary: string;
  details: string;
  timestamp: string;
};

export default function SearchSuccessClient() {
  const [report, setReport] = useState<Report | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    const fetchAnalysis = async () => {
      try {
        // Get the stored session data
        const storedData = localStorage.getItem("scabbiaCheckupData");
        if (!storedData) {
          throw new Error("Nessun dato trovato. Per favore, completa il questionario.");
        }

        const sessionData: SessionData = JSON.parse(storedData);
        
        // Call the analyze-answers API
        const response = await fetch("/api/analyze-answers", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ input: sessionData.input }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Errore durante l'analisi delle risposte");
        }

        const { answer } = await response.json();

        // Create the report with the AI analysis
        const report: Report = {
          probability: sessionData.probability,
          summary: sessionData.summary,
          details: sessionData.details,
          analysis: answer,
        };

        // Save the complete report
        localStorage.setItem("scabbiaResult", JSON.stringify(report));
        setReport(report);
      } catch (err) {
        console.error("Error:", err);
        setError(err instanceof Error ? err.message : "Si è verificato un errore");
      } finally {
        setLoading(false);
      }
    };

    if (sessionId) {
      fetchAnalysis();
    } else {
      // If no session ID, try to load existing result
      const stored = localStorage.getItem("scabbiaResult");
      if (stored) {
        try {
          setReport(JSON.parse(stored));
        } catch {
          setError("Errore nel caricamento del risultato");
        }
      } else {
        setError("Nessun risultato trovato. Per favore, completa il questionario.");
      }
      setLoading(false);
    }
  }, [sessionId]);

  if (loading) {
    return (
      <div className="flex flex-col items-center gap-4 p-8">
        <p>Elaborazione del risultato...</p>
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-foreground" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center gap-4 p-8 text-center">
        <h1 className="text-xl font-bold text-red-600">Errore</h1>
        <p className="text-red-500">{error}</p>
        <Link href="/scabbia-checker" className="mt-4 px-4 py-2 bg-blue-600 text-white rounded">
          Torna al questionario
        </Link>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center gap-6 p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold text-center">Risultato Analisi Scabbia</h1>
      
      {report ? (
        <div className="w-full space-y-6 bg-white p-6 rounded-lg shadow-md">
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h2 className="text-lg font-semibold text-blue-800 mb-2">Riepilogo</h2>
              <p className="text-gray-700">{report.summary}</p>
            </div>
            
            <div className="p-4 bg-gray-50 rounded-lg">
              <h2 className="text-lg font-semibold text-gray-800 mb-2">Dettagli</h2>
              <p className="text-gray-700">{report.details}</p>
              <p className="mt-2 font-medium">
                Probabilità stimata: <span className="text-blue-600">{report.probability}%</span>
              </p>
            </div>
            
            {report.analysis && (
              <div className="p-4 bg-green-50 rounded-lg">
                <h2 className="text-lg font-semibold text-green-800 mb-2">Analisi Approfondita</h2>
                <p className="text-gray-700 whitespace-pre-line">{report.analysis}</p>
              </div>
            )}
          </div>
          
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-500 text-center">
              Questa analisi non sostituisce il parere di un medico. Si consiglia di consultare un professionista sanitario per una diagnosi accurata.
            </p>
          </div>
        </div>
      ) : (
        <div className="text-center">
          <p className="text-gray-600">Nessun risultato disponibile</p>
        </div>
      )}
      
      <div className="mt-6 w-full flex flex-col sm:flex-row justify-center gap-4">
        <Link 
          href="/scabbia-checker" 
          className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-center"
        >
          Nuova Analisi
        </Link>
        <Link 
          href="/" 
          className="px-6 py-2 bg-gray-100 text-gray-800 rounded hover:bg-gray-200 text-center"
        >
          Torna alla Home
        </Link>
      </div>
    </div>
  );
}
