@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Medical Color Palette */
    --background: 0 0% 100%;
    --foreground: 210 40% 8%;
    --card: 0 0% 100%;
    --card-foreground: 210 40% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 8%;

    /* Primary - Medical Blue */
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 210 100% 95%;
    --primary-dark: 210 100% 40%;

    /* Secondary - Teal */
    --secondary: 180 25% 95%;
    --secondary-foreground: 180 25% 15%;
    --secondary-accent: 180 84% 60%;

    /* Success - Green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --success-light: 142 76% 95%;

    /* Warning - Amber */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --warning-light: 38 92% 95%;

    /* Error - Red */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --destructive-light: 0 84% 95%;

    /* Neutral Colors */
    --muted: 210 40% 98%;
    --muted-foreground: 210 40% 45%;
    --accent: 210 40% 96%;
    --accent-foreground: 210 40% 10%;

    /* Border & Input */
    --border: 210 40% 90%;
    --input: 210 40% 90%;
    --ring: 210 100% 50%;

    /* Chart Colors */
    --chart-1: 210 100% 50%;
    --chart-2: 180 84% 60%;
    --chart-3: 142 76% 36%;
    --chart-4: 38 92% 50%;
    --chart-5: 0 84% 60%;

    /* Border Radius */
    --radius: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  .dark {
    --background: 210 40% 3%;
    --foreground: 210 40% 98%;
    --card: 210 40% 4%;
    --card-foreground: 210 40% 98%;
    --popover: 210 40% 4%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 100% 60%;
    --primary-foreground: 210 40% 3%;
    --primary-light: 210 100% 10%;
    --primary-dark: 210 100% 70%;

    --secondary: 210 40% 10%;
    --secondary-foreground: 210 40% 98%;
    --secondary-accent: 180 84% 50%;

    --success: 142 76% 46%;
    --success-foreground: 210 40% 3%;
    --success-light: 142 76% 10%;

    --warning: 38 92% 60%;
    --warning-foreground: 210 40% 3%;
    --warning-light: 38 92% 10%;

    --destructive: 0 84% 70%;
    --destructive-foreground: 210 40% 3%;
    --destructive-light: 0 84% 10%;

    --muted: 210 40% 10%;
    --muted-foreground: 210 40% 65%;
    --accent: 210 40% 10%;
    --accent-foreground: 210 40% 98%;

    --border: 210 40% 18%;
    --input: 210 40% 18%;
    --ring: 210 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
}

@layer components {
  /* Container utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Text utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-secondary-accent bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }
}

@layer utilities {
  /* Custom shadows */
  .shadow-soft {
    box-shadow: var(--shadow);
  }

  .shadow-medium {
    box-shadow: var(--shadow-md);
  }

  .shadow-large {
    box-shadow: var(--shadow-lg);
  }

  .shadow-extra-large {
    box-shadow: var(--shadow-xl);
  }

  /* Glass effect */
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .glass-dark {
    @apply bg-black/80 backdrop-blur-sm border border-white/10;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
