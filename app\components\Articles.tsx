import Link from 'next/link'
import { ArrowR<PERSON>, Clock, User, BookOpen } from 'lucide-react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const articles = [
  {
    id: 1,
    title: "Che cos'è la scabbia",
    excerpt: "Tutto quello che devi sapere sulla scabbia: cause, sintomi e come riconoscerla.",
    href: '/cos-e-la-scabbia',
    readTime: '5 min',
    category: 'Informazioni',
    icon: BookOpen,
    gradient: 'from-blue-500 to-blue-600'
  },
  {
    id: 2,
    title: 'Sintomi e diagnosi',
    excerpt: "Come riconoscere i sintomi della scabbia e quando consultare un medico.",
    href: '/sintomi',
    readTime: '4 min',
    category: 'Diagnosi',
    icon: User,
    gradient: 'from-green-500 to-green-600'
  },
  {
    id: 3,
    title: 'Prevenzione efficace',
    excerpt: "Strategie e consigli pratici per prevenire il contagio della scabbia.",
    href: '/prevenzione',
    readTime: '6 min',
    category: 'Prevenzione',
    icon: Clock,
    gradient: 'from-purple-500 to-purple-600'
  },
  {
    id: 4,
    title: 'Cure e trattamenti',
    excerpt: "I trattamenti più efficaci per curare la scabbia e tornare in salute.",
    href: '/cura',
    readTime: '7 min',
    category: 'Trattamento',
    icon: BookOpen,
    gradient: 'from-orange-500 to-orange-600'
  }
]

export default function Articles() {
  return (
    <section id="articoli" className="py-20 bg-muted/30">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="mb-4">
            Risorse Mediche
          </Badge>
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground">
            Articoli e Guide Utili
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Informazioni complete e affidabili sulla scabbia, scritte da esperti medici
            per aiutarti a comprendere meglio questa condizione.
          </p>
        </div>

        {/* Articles Grid */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4">
          {articles.map((article) => (
            <Link key={article.id} href={article.href}>
              <Card className="group h-full hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  {/* Icon and Category */}
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${article.gradient} flex items-center justify-center shadow-medium`}>
                      <article.icon className="w-6 h-6 text-white" />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {article.category}
                    </Badge>
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2">
                    {article.title}
                  </h3>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* Excerpt */}
                  <p className="text-muted-foreground text-sm leading-relaxed mb-6 line-clamp-3">
                    {article.excerpt}
                  </p>

                  {/* Footer */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock className="w-3 h-3 mr-1" />
                      {article.readTime}
                    </div>

                    <div className="flex items-center text-primary text-sm font-medium group-hover:text-primary-dark transition-colors">
                      Leggi di più
                      <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* View All Link */}
        <div className="text-center mt-12">
          <Link href="/blog">
            <div className="inline-flex items-center text-primary hover:text-primary-dark font-medium group">
              Vedi tutti gli articoli
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </div>
          </Link>
        </div>
      </div>
    </section>
  )
}

