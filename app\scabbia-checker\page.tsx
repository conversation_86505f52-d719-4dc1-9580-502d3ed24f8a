"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import { useOpenAI } from "@/lib/api-utils";
import { toast } from "react-hot-toast";
import { scabiesOraclePrompt } from "@/lib/scabiesPrompt";

const questions = [
  "Hai prurito intenso, soprattutto di notte?",
  "Hai notato piccoli rilievi o vescicole sulla pelle?",
  "Qualcuno nella tua famiglia ha sintomi simili?",
  "Hai notato linee sottili sulla pelle che sembrano cunicoli?",
  "Le zone colpite includono mani, polsi, ascelle o genitali?",
  "Hai dormito di recente a stretto contatto con altre persone?",
  "Hai la pelle irritata tra le dita?",
  "I sintomi peggiorano dopo bagni caldi?",
  "Hai notato crosticine o noduli?",
  "Ti è stata diagnosticata la scabbia in passato?",
];

export default function ScabbiaChecker() {
  const [index, setIndex] = useState(0);
  const [answers, setAnswers] = useState<boolean[]>([]);
  const [loading, setLoading] = useState(false);
  const [showPay, setShowPay] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const { callAPI } = useOpenAI();

  // Show error toast when API error occurs
  useEffect(() => {
    if (apiError) {
      toast.error(apiError, {
        position: 'top-center',
        duration: 5000,
      });
    }
  }, [apiError]);


  const handleAnswer = (value: boolean) => {
    const newAnswers = [...answers, value];
    setAnswers(newAnswers);

    const nextIndex = index + 1;
    if (nextIndex >= questions.length) {
      finish(newAnswers);
      return;
    }

    if (nextIndex === 5) {
      if (newAnswers.filter((a) => a).length < 3) {
        finish(newAnswers);
        return;
      }
    }

    setIndex(nextIndex);
  };

  const finish = async (finalAnswers: boolean[]) => {
    setLoading(true);
    setApiError(null);
    
    try {
      const count = finalAnswers.filter((a) => a).length;
      const probability = Math.round((count / questions.length) * 100);
      const summary =
        count >= 5
          ? "In base alle tue risposte potresti avere la scabbia. Contatta un medico per una diagnosi certa."
          : "Dalle tue risposte sembra improbabile che tu abbia la scabbia.";

      const details =
        count >= 5
          ? `Hai risposto \"Sì\" a ${count} domande su ${questions.length}, in particolare ad alcune tipiche della scabbia.`
          : `Solo ${count} risposte su ${questions.length} indicano sintomi compatibili.`;

      // Prepare the input in the format expected by scabiesOraclePrompt
      const symptoms = {
        itching_intensity: finalAnswers[0] ? "severe" : "none",
        itching_worse_at_night: finalAnswers[0] || false,
        rash_present: finalAnswers[1] || false,
        rash_locations: finalAnswers[5] 
          ? ["between_fingers", "wrist_folds", "armpits", "penis"]
          : finalAnswers[6] 
            ? ["between_fingers"] 
            : [],
        visible_burrows: finalAnswers[3] || false,
        onset_days: 30,
        close_contact_scabies: finalAnswers[2] || false,
        lives_in_crowded_setting: finalAnswers[5] || false,
        attempted_treatment: finalAnswers[9] ? "other" : "none",
        skin_scraping_positive: false,
        immune_status: "normal",
        thick_crusts_on_skin: finalAnswers[8] || false,
      };

      const input = {
        messages: [
          {
            role: 'system' as const,
            content: scabiesOraclePrompt
          },
          {
            role: 'user' as const,
            content: JSON.stringify(symptoms)
          }
        ],
        temperature: 0.3,  // Lower temperature for more consistent, structured output
        max_tokens: 1000
      };

      // Call the AI API with rate limiting and error handling
      await callAPI(input);

      // Store the input data for use after the payment
      const sessionData = {
        input: JSON.parse(input.messages[1].content), // Store the parsed symptoms as input
        probability,
        summary,
        details,
        timestamp: new Date().toISOString(),
      };

      localStorage.setItem("scabbiaCheckupData", JSON.stringify(sessionData));
      setShowPay(true);
    } catch (error) {
      console.error("Error in finish function:", error);
      setApiError("Si è verificato un errore durante l'elaborazione. Riprova più tardi.");
    } finally {
      setLoading(false);
    }
  };

  const startCheckout = async () => {
    try {
      setLoading(true);
      const res = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          // Include any necessary data for the checkout session
          return_url: `${window.location.origin}/scabbia-checker/success`,
        }),
      });

      if (!res.ok) {
        const errorText = await res.text();
        console.error("Checkout session error", errorText);
        alert("Si è verificato un errore durante l'elaborazione del pagamento. Riprova più tardi.");
        setLoading(false);
        return;
      }

      const data = await res.json();
      if (data.url) {
        // Store a flag in localStorage to indicate payment is in progress
        localStorage.setItem("paymentInProgress", "true");
        window.location.href = data.url;
      } else {
        throw new Error("URL di checkout non ricevuto");
      }
    } catch (err) {
      console.error("Failed to start checkout", err);
      alert("Impossibile avviare il processo di pagamento. Riprova più tardi.");
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center gap-4 p-8">
        <p>Elaborazione delle risposte...</p>
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-foreground" />
      </div>
    );
  }

  if (showPay) {
    return (
      <div className="flex flex-col items-center gap-6 p-8 max-w-md mx-auto text-center">
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100 w-full">
          <h2 className="text-xl font-bold text-blue-800 mb-3">Analisi Pronta</h2>
          <p className="text-gray-700 mb-4">
            Per visualizzare l&apos;analisi completa e personalizzata delle tue risposte, 
            completa il pagamento di <span className="font-bold">soli 1€</span>.
          </p>
          <p className="text-sm text-gray-500 mb-6">
            Il pagamento è sicuro e protetto tramite Stripe.
          </p>
          <button
            onClick={startCheckout}
            disabled={loading}
            className={`w-full py-3 px-6 rounded-lg font-medium flex items-center justify-center gap-2 ${
              loading 
                ? 'bg-blue-400 cursor-not-allowed' 
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white transition-colors`}
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Elaborazione...
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                Paga 1€ con Stripe
              </>
            )}
          </button>
          <p className="text-xs text-gray-400 mt-3">
            Pagamento sicuro al 100% - Nessun dato della tua carta viene memorizzato sui nostri server
          </p>
        </div>
        
        <Link 
          href="/" 
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          Torna alla Home
        </Link>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center gap-6 p-8">
      <p className="text-lg font-semibold text-center">{questions[index]}</p>
      <div className="flex gap-4">
        <button
          onClick={() => handleAnswer(true)}
          className="bg-foreground text-background px-4 py-2 rounded"
        >
          Sì
        </button>
        <button
          onClick={() => handleAnswer(false)}
          className="bg-muted text-foreground px-4 py-2 rounded"
        >
          No
        </button>
      </div>
      <p className="text-sm text-muted-foreground">
        Domanda {index + 1} di {questions.length}
      </p>
    </div>
  );
}
